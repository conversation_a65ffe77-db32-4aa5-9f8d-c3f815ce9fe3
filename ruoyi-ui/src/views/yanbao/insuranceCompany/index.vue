<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="保险公司简称" prop="name">
            <el-input
               v-model="queryParams.name"
               placeholder="请输入保险公司简称"
               clearable
               style="width: 200px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>

         <el-form-item label="保险公司全称" prop="fullName">
            <el-input
               v-model="queryParams.fullName"
               placeholder="请输入保险公司全称"
               clearable
               style="width: 200px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>

         <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="保险公司状态" clearable style="width: 200px">
               <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['yanbao:insuranceCompany:add']"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="success"
               plain
               icon="Edit"
               :disabled="single"
               @click="handleUpdate"
               v-hasPermi="['yanbao:insuranceCompany:edit']"
            >修改</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['yanbao:insuranceCompany:remove']"
            >删除</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="insuranceCompanyList" @selection-change="handleSelectionChange" @sort-change="handleSortChange" stripe>
         <el-table-column type="selection" width="60" align="center" />
         <el-table-column label="保险公司简称" align="center" prop="name" :show-overflow-tooltip="true" sortable="custom" />
         <el-table-column label="保险公司全称" align="center" prop="fullName" :show-overflow-tooltip="true" />
         <el-table-column label="排序" align="center" prop="sort" sortable="custom">
            <template #default="scope">
               <el-input-number
                  v-if="scope.row.editingSort"
                  v-model="scope.row.sort"
                  :min="0"
                  :max="9999"
                  size="small"
                  controls-position="right"
                  @blur="handleSortSave(scope.row)"
                  @keyup.enter="handleSortSave(scope.row)"
                  @keyup.esc="handleSortCancel(scope.row)"
                  style="width: 90px;"
                  :data-row-id="scope.row.id"
               />
               <span
                  v-else
                  @click="handleSortEdit(scope.row)"
                  style="cursor: pointer; padding: 6px 12px; border-radius: 4px; background-color: #f0f9ff; border: 1px dashed #409eff; display: inline-block; min-width: 40px; text-align: center;"
                  title="点击编辑排序"
                  v-hasPermi="['yanbao:insuranceCompany:edit']"
               >
                  {{ scope.row.sort }}
               </span>
            </template>
         </el-table-column>
         <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
               <el-switch
                  v-model="scope.row.status"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleStatusChange(scope.row)"
                  v-hasPermi="['yanbao:insuranceCompany:edit']"
               ></el-switch>
            </template>
         </el-table-column>
         <el-table-column label="创建时间" align="center" prop="createdAt" width="180" sortable="custom">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button
                  type="text"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['yanbao:insuranceCompany:edit']"
               >修改</el-button>
               <el-button
                  type="text"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['yanbao:insuranceCompany:remove']"
               >删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改保险公司管理对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
         <el-form ref="insuranceCompanyRef" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="保险公司简称" prop="name">
               <el-input v-model="form.name" placeholder="请输入保险公司简称" />
            </el-form-item>
            <el-form-item label="保险公司全称" prop="fullName">
               <el-input v-model="form.fullName" placeholder="请输入保险公司全称" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
               <el-radio-group v-model="form.status">
                  <el-radio :label="1">合作中</el-radio>
                  <el-radio :label="0">停止合作</el-radio>
               </el-radio-group>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
               <el-input-number v-model="form.sort" :min="0" :max="9999" controls-position="right" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="InsuranceCompany">
import { listInsuranceCompany, getInsuranceCompany, delInsuranceCompany, addInsuranceCompany, updateInsuranceCompany, changeInsuranceCompanyStatus } from "@/api/yanbao/insuranceCompany";

const { proxy } = getCurrentInstance();

const insuranceCompanyList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    fullName: null,
    status: null,
  },
  rules: {
    name: [
      { required: true, message: "保险公司简称不能为空", trigger: "blur" }
    ],
    fullName: [
      { required: true, message: "保险公司全称不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ],
    sort: [
      { required: true, message: "排序不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 状态选项
const statusOptions = ref([
  { label: "合作中", value: 1 },
  { label: "停止合作", value: 0 }
]);

/** 查询保险公司管理列表 */
function getList() {
  loading.value = true;
  listInsuranceCompany(queryParams.value).then(response => {
    insuranceCompanyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    fullName: null,
    status: 1,
    sort: 0
  };
  proxy.resetForm("insuranceCompanyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加保险公司管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getInsuranceCompany(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改保险公司管理";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["insuranceCompanyRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateInsuranceCompany(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addInsuranceCompany(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const deleteIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除保险公司管理编号为"' + deleteIds + '"的数据项？').then(function() {
    return delInsuranceCompany(deleteIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 保险公司状态修改 */
function handleStatusChange(row) {
  let text = row.status === 1 ? "启用" : "停用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"保险公司吗？').then(function() {
    return changeInsuranceCompanyStatus(row.id, row.status);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function() {
    row.status = row.status === 0 ? 1 : 0;
  });
}

/** 排序字段 */
function handleSortChange(column) {
  queryParams.value.orderByColumn = column.prop;
  queryParams.value.isAsc = column.order === "ascending" ? "asc" : "desc";
  getList();
}

// 排序编辑相关方法
function handleSortEdit(row) {
  row.editingSort = true;
  row.originalSort = row.sort;
}

function handleSortSave(row) {
  if (row.sort !== row.originalSort) {
    updateInsuranceCompany(row).then(response => {
      proxy.$modal.msgSuccess("排序修改成功");
      row.editingSort = false;
      delete row.originalSort;
      getList();
    }).catch(() => {
      row.sort = row.originalSort;
      row.editingSort = false;
      delete row.originalSort;
    });
  } else {
    row.editingSort = false;
    delete row.originalSort;
  }
}

function handleSortCancel(row) {
  row.sort = row.originalSort;
  row.editingSort = false;
  delete row.originalSort;
}

onMounted(() => {
  getList();
});
</script>
