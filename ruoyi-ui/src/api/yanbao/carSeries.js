import request from '@/utils/request.js'

// 查询车系管理列表
export function listCarSeries(query) {
  return request({
    url: '/yanbao/carSeries/list',
    method: 'get',
    params: query
  })
}

// 查询车系管理详细
export function getCarSeries(id) {
  return request({
    url: '/yanbao/carSeries/' + id,
    method: 'get'
  })
}

// 新增车系管理
export function addCarSeries(data) {
  return request({
    url: '/yanbao/carSeries',
    method: 'post',
    data: data
  })
}

// 修改车系管理
export function updateCarSeries(data) {
  return request({
    url: '/yanbao/carSeries',
    method: 'put',
    data: data
  })
}

// 删除车系管理
export function delCarSeries(id) {
  return request({
    url: '/yanbao/carSeries/' + id,
    method: 'delete'
  })
}

// 车系状态修改
export function changeCarSeriesStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/yanbao/carSeries/changeStatus',
    method: 'put',
    data: data
  })
}

// 获取车系选择框列表
export function optionSelectCarSeries() {
  return request({
    url: '/yanbao/carSeries/optionSelect',
    method: 'get'
  })
}

// 批量更新车系排序
export function updateCarSeriesSort(data) {
  return request({
    url: '/yanbao/carSeries/sort',
    method: 'put',
    data: data
  })
}
