import request from '@/utils/request.js'

// 查询保险公司管理列表
export function listInsuranceCompany(query) {
  return request({
    url: '/yanbao/insuranceCompany/list',
    method: 'get',
    params: query
  })
}

// 查询保险公司管理详细
export function getInsuranceCompany(id) {
  return request({
    url: '/yanbao/insuranceCompany/' + id,
    method: 'get'
  })
}

// 新增保险公司管理
export function addInsuranceCompany(data) {
  return request({
    url: '/yanbao/insuranceCompany',
    method: 'post',
    data: data
  })
}

// 修改保险公司管理
export function updateInsuranceCompany(data) {
  return request({
    url: '/yanbao/insuranceCompany',
    method: 'put',
    data: data
  })
}

// 删除保险公司管理
export function delInsuranceCompany(id) {
  return request({
    url: '/yanbao/insuranceCompany/' + id,
    method: 'delete'
  })
}

// 保险公司状态修改
export function changeInsuranceCompanyStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/yanbao/insuranceCompany/changeStatus',
    method: 'put',
    data: data
  })
}

// 获取保险公司选择框列表
export function optionSelectInsuranceCompany(status) {
  return request({
    url: '/yanbao/insuranceCompany/optionSelect',
    method: 'get',
    params: { status }
  })
}

// 批量更新保险公司排序
export function updateInsuranceCompanySort(data) {
  return request({
    url: '/yanbao/insuranceCompany/sort',
    method: 'put',
    data: data
  })
}
