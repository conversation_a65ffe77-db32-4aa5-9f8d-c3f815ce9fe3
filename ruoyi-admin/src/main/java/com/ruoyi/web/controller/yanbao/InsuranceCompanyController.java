package com.ruoyi.web.controller.yanbao;

import java.util.List;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.yanbao.entity.InsuranceCompany;
import com.ruoyi.yanbao.service.InsuranceCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 保险公司管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/yanbao/insuranceCompany")
public class InsuranceCompanyController extends BaseController
{
    @Autowired
    private InsuranceCompanyService insuranceCompanyService;

    /**
     * 查询保险公司管理列表
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insuranceCompany:list')")
    @GetMapping("/list")
    public TableDataInfo list(InsuranceCompany insuranceCompany)
    {
        startPage();
        List<InsuranceCompany> list = insuranceCompanyService.selectInsuranceCompanyList(insuranceCompany);
        return getDataTable(list);
    }

    /**
     * 获取保险公司管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insuranceCompany:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(insuranceCompanyService.getById(id));
    }

    /**
     * 新增保险公司管理
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insuranceCompany:add')")
    @Log(title = "保险公司管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody InsuranceCompany insuranceCompany)
    {
        if (!insuranceCompanyService.checkInsuranceCompanyNameUnique(insuranceCompany))
        {
            return error("新增保险公司'" + insuranceCompany.getName() + "'失败，保险公司名称已存在");
        }
        insuranceCompany.setCreatedBy(getUsername());
        return toAjax(insuranceCompanyService.save(insuranceCompany));
    }

    /**
     * 修改保险公司管理
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insuranceCompany:edit')")
    @Log(title = "保险公司管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody InsuranceCompany insuranceCompany)
    {
        if (!insuranceCompanyService.checkInsuranceCompanyNameUnique(insuranceCompany))
        {
            return error("修改保险公司'" + insuranceCompany.getName() + "'失败，保险公司名称已存在");
        }
        insuranceCompany.setChangedBy(getUsername());
        return toAjax(insuranceCompanyService.updateById(insuranceCompany));
    }

    /**
     * 删除保险公司管理
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insuranceCompany:remove')")
    @Log(title = "保险公司管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(insuranceCompanyService.deleteInsuranceCompanyByIds(ids));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insuranceCompany:edit')")
    @Log(title = "保险公司管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody InsuranceCompany insuranceCompany)
    {
        return toAjax(insuranceCompanyService.changeStatus(insuranceCompany.getId(), insuranceCompany.getStatus()));
    }

    /**
     * 获取保险公司选择框列表
     */
    @GetMapping("/optionSelect")
    public AjaxResult optionSelect(Integer status)
    {
        List<InsuranceCompany> insuranceCompanies = insuranceCompanyService.selectInsuranceCompanyAll(status);
        return success(insuranceCompanies);
    }

    /**
     * 排序操作
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insuranceCompany:edit')")
    @Log(title = "保险公司管理", businessType = BusinessType.UPDATE)
    @PutMapping("/sort")
    public AjaxResult updateSort(@RequestBody List<InsuranceCompany> insuranceCompanyList)
    {
        return toAjax(insuranceCompanyService.updateInsuranceCompanySort(insuranceCompanyList));
    }
}
