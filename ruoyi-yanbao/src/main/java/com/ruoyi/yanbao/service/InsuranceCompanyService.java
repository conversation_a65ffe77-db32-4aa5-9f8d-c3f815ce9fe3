package com.ruoyi.yanbao.service;

import java.util.List;
import com.ruoyi.yanbao.entity.InsuranceCompany;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 保险公司管理 服务类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface InsuranceCompanyService extends IService<InsuranceCompany> {

    /**
     * 查询保险公司管理列表
     *
     * @param insuranceCompany 保险公司管理
     * @return 保险公司管理集合
     */
    public List<InsuranceCompany> selectInsuranceCompanyList(InsuranceCompany insuranceCompany);

    /**
     * 批量删除保险公司管理
     *
     * @param ids 需要删除的保险公司管理主键集合
     * @return 结果
     */
    public int deleteInsuranceCompanyByIds(Long[] ids);

    /**
     * 校验保险公司名称是否唯一
     *
     * @param insuranceCompany 保险公司信息
     * @return 结果
     */
    public boolean checkInsuranceCompanyNameUnique(InsuranceCompany insuranceCompany);

    /**
     * 查询所有保险公司
     *
     * @return 保险公司列表
     */
    public List<InsuranceCompany> selectInsuranceCompanyAll(Integer status);

    /**
     * 批量更新保险公司排序
     *
     * @param insuranceCompanyList 保险公司列表
     * @return 结果
     */
    public int updateInsuranceCompanySort(List<InsuranceCompany> insuranceCompanyList);

    /**
     * 启用，停用
     * @param id
     * @param status
     * @return
     */
    boolean changeStatus(Long id, Integer status);
}
