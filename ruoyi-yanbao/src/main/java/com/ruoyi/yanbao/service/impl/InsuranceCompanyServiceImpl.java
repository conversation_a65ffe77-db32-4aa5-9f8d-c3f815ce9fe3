package com.ruoyi.yanbao.service.impl;

import java.util.List;
import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sql.SqlUtil;
import com.ruoyi.yanbao.entity.InsuranceCompany;
import com.ruoyi.yanbao.mapper.InsuranceCompanyMapper;
import com.ruoyi.yanbao.service.InsuranceCompanyService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 保险公司管理 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class InsuranceCompanyServiceImpl extends ServiceImpl<InsuranceCompanyMapper, InsuranceCompany> implements InsuranceCompanyService {

    @Autowired
    private InsuranceCompanyMapper insuranceCompanyMapper;

    /**
     * 查询保险公司管理列表
     *
     * @param insuranceCompany 保险公司管理
     * @return 保险公司管理
     */
    @Override
    public List<InsuranceCompany> selectInsuranceCompanyList(InsuranceCompany insuranceCompany) {
        QueryWrapper<InsuranceCompany> queryWrapper = new QueryWrapper<>();

        if (StringUtils.isNotEmpty(insuranceCompany.getName())) {
            queryWrapper.like("name", insuranceCompany.getName());
        }
        if (StringUtils.isNotEmpty(insuranceCompany.getFullName())) {
            queryWrapper.like("full_name", insuranceCompany.getFullName());
        }
        if (insuranceCompany.getStatus() != null) {
            queryWrapper.eq("status", insuranceCompany.getStatus());
        }

        // 处理排序
        String orderByColumn = insuranceCompany.getOrderByColumn();
        String isAsc = insuranceCompany.getIsAsc();
        if (StringUtils.isNotEmpty(orderByColumn) && StringUtils.isNotEmpty(isAsc)) {
            String orderBy = SqlUtil.escapeOrderBySql(orderByColumn);
            if ("asc".equals(isAsc)) {
                queryWrapper.orderByAsc(orderBy);
            } else {
                queryWrapper.orderByDesc(orderBy);
            }
        } else {
            queryWrapper.orderByAsc("sort").orderByDesc("created_at");
        }

        return insuranceCompanyMapper.selectList(queryWrapper);
    }

    /**
     * 批量删除保险公司管理
     *
     * @param ids 需要删除的保险公司管理主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteInsuranceCompanyByIds(Long[] ids) {
        int n = 0;
        for (Long id : ids) {
            InsuranceCompany ic = new InsuranceCompany();
            ic.setId(id);
            ic.setChangedAt(new Date());
            ic.setChangedBy(SecurityUtils.getLoginUser().getUsername());
            ic.setIsDelete(1);
            n = n + insuranceCompanyMapper.updateById(ic);
        }
        return n;
    }

    /**
     * 校验保险公司名称是否唯一
     *
     * @param insuranceCompany 保险公司信息
     * @return 结果
     */
    @Override
    public boolean checkInsuranceCompanyNameUnique(InsuranceCompany insuranceCompany) {
        Long insuranceCompanyId = StringUtils.isNull(insuranceCompany.getId()) ? -1L : insuranceCompany.getId();
        QueryWrapper<InsuranceCompany> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", insuranceCompany.getName());
        InsuranceCompany info = insuranceCompanyMapper.selectOne(queryWrapper);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != insuranceCompanyId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 查询所有保险公司
     *
     * @return 保险公司列表
     */
    @Override
    public List<InsuranceCompany> selectInsuranceCompanyAll(Integer status) {
        QueryWrapper<InsuranceCompany> queryWrapper = new QueryWrapper<>();
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        queryWrapper.orderByDesc("status").orderByDesc("sort").orderByAsc("name");
        return insuranceCompanyMapper.selectList(queryWrapper);
    }

    /**
     * 批量更新保险公司排序
     *
     * @param insuranceCompanyList 保险公司列表
     * @return 结果
     */
    @Override
    @Transactional
    public int updateInsuranceCompanySort(List<InsuranceCompany> insuranceCompanyList) {
        int result = 0;
        for (InsuranceCompany insuranceCompany : insuranceCompanyList) {
            insuranceCompany.setChangedBy(SecurityUtils.getLoginUser().getUsername());
            result += insuranceCompanyMapper.updateById(insuranceCompany);
        }
        return result;
    }

    @Override
    public boolean changeStatus(Long id, Integer status) {
        InsuranceCompany insuranceCompany = getById(id);
        if (insuranceCompany != null) {
            insuranceCompany.setStatus(status);
            insuranceCompany.setChangedBy(SecurityUtils.getLoginUser().getUsername());
            return updateById(insuranceCompany);
        }
        return false;
    }
}
